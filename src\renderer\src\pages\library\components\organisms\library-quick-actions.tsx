import { useCallback } from "react";
import { useTranslation } from "react-i18next";
import {
  HeartIcon,
  HeartFillIcon,
  GearIcon,
  PlusIcon,
  FileDirectoryIcon,
} from "@primer/octicons-react";

import type { LibraryGame } from "@types";

import "./library-quick-actions.scss";

interface LibraryQuickActionsProps {
  game: LibraryGame;
  onToggleFavorite?: (game: LibraryGame) => void;
  onShowOptions?: (game: LibraryGame) => void;
  onAddToCollection?: (game: LibraryGame) => void;
  onOpenFolder?: (game: LibraryGame) => void;
  className?: string;
}

export function LibraryQuickActions({
  game,
  onToggleFavorite,
  onShowOptions,
  onAddToCollection,
  onOpenFolder,
  className = "",
}: LibraryQuickActionsProps) {
  const { t } = useTranslation("library");

  const handleToggleFavorite = useCallback(() => {
    onToggleFavorite?.(game);
  }, [onToggleFavorite, game]);

  const handleShowOptions = useCallback(() => {
    onShowOptions?.(game);
  }, [onShowOptions, game]);

  const handleAddToCollection = useCallback(() => {
    onAddToCollection?.(game);
  }, [onAddToCollection, game]);

  const handleOpenFolder = useCallback(async () => {
    if (game.executablePath) {
      await window.electron.openGameExecutablePath(game.shop, game.objectId);
    }
  }, [game]);

  const isInstalled = Boolean(game.executablePath);
  const isFavorite = Boolean(game.isFavorite);

  return (
    <div className={`library-quick-actions ${className}`}>
      {/* Top Row - Primary Action */}
      <div className="library-quick-actions__top-row">
        <button
          type="button"
          className="library-quick-actions__button library-quick-actions__button--primary"
          onClick={handleShowOptions}
          title={t("game_options")}
        >
          <GearIcon size={16} />
          <span>{t("options")}</span>
        </button>
      </div>

      {/* Bottom Row - Secondary Actions */}
      <div className="library-quick-actions__bottom-row">
        {/* Favorite Toggle */}
        <button
          type="button"
          className={`library-quick-actions__button library-quick-actions__button--icon ${
            isFavorite ? "library-quick-actions__button--active" : ""
          }`}
          onClick={handleToggleFavorite}
          title={isFavorite ? t("remove_from_favorites") : t("add_to_favorites")}
        >
          {isFavorite ? <HeartFillIcon size={16} /> : <HeartIcon size={16} />}
        </button>

        {/* Add to Collection */}
        <button
          type="button"
          className="library-quick-actions__button library-quick-actions__button--icon"
          onClick={handleAddToCollection}
          title={t("add_to_collection")}
        >
          <PlusIcon size={16} />
        </button>

        {/* Open Folder - Only for installed games */}
        {isInstalled && (
          <button
            type="button"
            className="library-quick-actions__button library-quick-actions__button--icon"
            onClick={handleOpenFolder}
            title={t("open_folder")}
          >
            <FileDirectoryIcon size={16} />
          </button>
        )}
      </div>
    </div>
  );
}
