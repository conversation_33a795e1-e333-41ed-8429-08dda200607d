import { DownloadIcon, PeopleIcon } from "@primer/octicons-react";
import type { GameStats } from "@types";

import SteamLogo from "@renderer/assets/steam-logo.svg?react";

import "./game-card.scss";

import { useTranslation } from "react-i18next";
import { Badge } from "../badge/badge";
import { OptimizedImage } from "../optimized-image/optimized-image";
import { useCallback, useState, useMemo } from "react";
import { useFormat, useRepacks } from "@renderer/hooks";

export interface GameCardProps
  extends React.DetailedHTMLProps<
    React.ButtonHTMLAttributes<HTMLButtonElement>,
    HTMLButtonElement
  > {
  game: any;
}

const shopIcon = {
  steam: <SteamLogo className="game-card__shop-icon" />,
};

export function GameCard({ game, ...props }: GameCardProps) {
  const { t } = useTranslation("game_card");

  const [stats, setStats] = useState<GameStats | null>(null);

  const { getRepacksForObjectId } = useRepacks();
  const repacks = getRepacksForObjectId(game.objectId);

  const uniqueRepackers = Array.from(
    new Set(repacks.map((repack) => repack.repacker))
  );

  const handleHover = useCallback(() => {
    if (!stats) {
      window.electron.getGameStats(game.objectId, game.shop).then((stats) => {
        setStats(stats);
      });
    }
  }, [game, stats]);

  const { numberFormatter } = useFormat();

  const firstThreeRepackers = useMemo(
    () => uniqueRepackers.slice(0, 3),
    [uniqueRepackers]
  );
  const remainingCount = useMemo(
    () => uniqueRepackers.length - 3,
    [uniqueRepackers]
  );

  return (
    <button
      {...props}
      type="button"
      className="game-card"
      onMouseEnter={handleHover}
    >
      <div className="game-card__backdrop">
        <div className="game-card__cover">
          <OptimizedImage
            src={game.libraryImageUrl}
            alt={game.title}
            className="optimized-image--full-size"
            loading="lazy"
            showLoadingSpinner={true}
            fadeInDuration={200}
          />
        </div>

        <div className="game-card__content">
          <div className="game-card__title-container">
            {shopIcon[game.shop]}
            <p className="game-card__title">{game.title}</p>
          </div>

          {uniqueRepackers.length > 0 ? (
            <ul className="game-card__download-options">
              {firstThreeRepackers.map((repacker) => (
                <li key={repacker}>
                  <Badge>{repacker}</Badge>
                </li>
              ))}
              {remainingCount > 0 && (
                <li>
                  <Badge>
                    +{remainingCount}{" "}
                    {t("game_card:available", { count: remainingCount })}
                  </Badge>
                </li>
              )}
            </ul>
          ) : (
            <p className="game-card__no-download-label">{t("no_downloads")}</p>
          )}

          <div className="game-card__specifics">
            <div className="game-card__specifics-item">
              <DownloadIcon />
              <span>
                {stats ? numberFormatter.format(stats.downloadCount) : "…"}
              </span>
            </div>
            <div className="game-card__specifics-item">
              <PeopleIcon />
              <span>
                {stats ? numberFormatter.format(stats.playerCount) : "…"}
              </span>
            </div>
          </div>
        </div>
      </div>
    </button>
  );
}
