@use "../../../../scss/globals.scss";

.library-quick-actions {
  display: grid;
  grid-template-rows: auto auto;
  gap: calc(globals.$spacing-unit * 0.75);
  padding: calc(globals.$spacing-unit * 1);
  width: 100%;

  &__top-row {
    display: flex;
    justify-content: stretch;
  }

  &__bottom-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(32px, 1fr));
    gap: calc(globals.$spacing-unit * 0.5);
    justify-items: center;
  }

  &__button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: calc(globals.$spacing-unit * 0.5);
    padding: calc(globals.$spacing-unit * 0.75) calc(globals.$spacing-unit * 1);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
    color: rgba(255, 255, 255, 0.9);
    font-size: globals.$small-font-size;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: 36px;
    backdrop-filter: blur(16px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    &:hover {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.08) 100%);
      border-color: rgba(255, 255, 255, 0.25);
      transform: translateY(-1px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &--primary {
      background: linear-gradient(135deg, globals.$brand-teal 0%, darken(globals.$brand-teal, 10%) 100%);
      border-color: rgba(255, 255, 255, 0.3);
      color: white;
      font-weight: 600;
      flex: 1;
      min-width: 100px;
      box-shadow: 0 2px 12px rgba(globals.$brand-teal, 0.4);

      &:hover {
        background: linear-gradient(135deg, lighten(globals.$brand-teal, 5%) 0%, globals.$brand-teal 100%);
        border-color: rgba(255, 255, 255, 0.4);
        box-shadow: 0 4px 20px rgba(globals.$brand-teal, 0.5);
      }
    }

    &--icon {
      padding: calc(globals.$spacing-unit * 0.75);
      min-width: 36px;
      width: 36px;
      height: 36px;
      justify-content: center;

      span {
        display: none;
      }
    }

    &--active {
      background: linear-gradient(135deg, rgba(globals.$brand-teal, 0.2) 0%, rgba(globals.$brand-teal, 0.1) 100%);
      border-color: rgba(globals.$brand-teal, 0.4);
      color: globals.$brand-teal;
      box-shadow: 0 2px 12px rgba(globals.$brand-teal, 0.3);

      &:hover {
        background: linear-gradient(135deg, rgba(globals.$brand-teal, 0.3) 0%, rgba(globals.$brand-teal, 0.2) 100%);
        border-color: rgba(globals.$brand-teal, 0.6);
      }
    }
  }



  // Responsive adjustments
  @media (max-width: 768px) {
    gap: calc(globals.$spacing-unit * 0.5);
    padding: calc(globals.$spacing-unit * 0.75);

    &__bottom-row {
      gap: calc(globals.$spacing-unit * 0.25);
    }

    &__button {
      padding: calc(globals.$spacing-unit * 0.5) calc(globals.$spacing-unit * 0.75);
      font-size: 11px;
      min-height: 32px;

      &--primary {
        min-width: 80px;
      }

      &--icon {
        padding: calc(globals.$spacing-unit * 0.5);
        min-width: 32px;
        width: 32px;
        height: 32px;
      }
    }
  }

  // Steam Deck optimizations
  @media (max-width: 1280px) and (max-height: 800px) {
    &__button {
      min-height: 40px;
      font-size: 14px;

      &--primary {
        min-width: 100px;
      }

      &--icon {
        min-width: 40px;
        width: 40px;
        height: 40px;
      }
    }
  }
}
